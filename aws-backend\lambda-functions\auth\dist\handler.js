"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
const USER_POOL_ID = process.env.COGNITO_USER_POOL_ID || '';
const CLIENT_ID = process.env.COGNITO_USER_POOL_CLIENT_ID || '';
const USERS_TABLE = 'Users';
function createCorsResponse(statusCode, body) {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}
async function createUserRecord(cognitoUserId, email, username) {
    try {
        const userId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const existingUser = await getUserByCognitoId(cognitoUserId);
        if (existingUser) {
            const updateCommand = new lib_dynamodb_1.UpdateCommand({
                TableName: 'Users',
                Key: { id: existingUser.id },
                UpdateExpression: 'SET email = :email, username = :username, updated_at = :updated_at',
                ExpressionAttributeValues: {
                    ':email': email,
                    ':username': username,
                    ':updated_at': now
                }
            });
            await docClient.send(updateCommand);
            return existingUser.id;
        }
        const putCommand = new lib_dynamodb_1.PutCommand({
            TableName: 'Users',
            Item: {
                id: userId,
                cognito_user_id: cognitoUserId,
                email: email,
                username: username,
                display_name: username,
                is_active: true,
                is_verified: false,
                created_at: now,
                updated_at: now
            }
        });
        await docClient.send(putCommand);
        return userId;
    }
    catch (error) {
        console.error('Failed to create user record:', error);
        return null;
    }
}
async function getUserByCognitoId(cognitoUserId) {
    try {
        const queryCommand = new lib_dynamodb_1.QueryCommand({
            TableName: 'Users',
            IndexName: 'CognitoUserIdIndex',
            KeyConditionExpression: 'cognito_user_id = :cognitoUserId',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId
            }
        });
        const result = await docClient.send(queryCommand);
        return result.Items?.[0] || null;
    }
    catch (error) {
        console.error('Failed to get user:', error);
        return null;
    }
}
async function signupHandler(event) {
    try {
        const body = JSON.parse(event.body || '{}');
        const email = body.email?.trim().toLowerCase();
        const password = body.password;
        const username = body.username?.trim();
        if (!email || !password || !username) {
            return createCorsResponse(400, {
                error: 'Email, password, and username are required'
            });
        }
        try {
            const createUserCommand = new client_cognito_identity_provider_1.AdminCreateUserCommand({
                UserPoolId: USER_POOL_ID,
                Username: email,
                UserAttributes: [
                    { Name: 'email', Value: email },
                    { Name: 'email_verified', Value: 'true' }
                ],
                TemporaryPassword: password,
                MessageAction: 'SUPPRESS'
            });
            const response = await cognitoClient.send(createUserCommand);
            const cognitoUserId = response.User?.Username || '';
            const setPasswordCommand = new client_cognito_identity_provider_1.AdminSetUserPasswordCommand({
                UserPoolId: USER_POOL_ID,
                Username: cognitoUserId,
                Password: password,
                Permanent: true
            });
            await cognitoClient.send(setPasswordCommand);
            const userId = await createUserRecord(cognitoUserId, email, username);
            if (!userId) {
                return createCorsResponse(500, {
                    error: 'Failed to create user profile'
                });
            }
            return createCorsResponse(201, {
                message: 'User created successfully',
                user: {
                    id: userId,
                    email,
                    username
                }
            });
        }
        catch (error) {
            if (error.name === 'UsernameExistsException') {
                return createCorsResponse(409, {
                    error: 'User already exists'
                });
            }
            console.error('Cognito signup error:', error);
            return createCorsResponse(500, {
                error: 'Failed to create user account'
            });
        }
    }
    catch (error) {
        console.error('Signup handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function signinHandler(event) {
    try {
        const body = JSON.parse(event.body || '{}');
        const email = body.email?.trim().toLowerCase();
        const password = body.password;
        if (!email || !password) {
            return createCorsResponse(400, {
                error: 'Email and password are required'
            });
        }
        try {
            const authCommand = new client_cognito_identity_provider_1.AdminInitiateAuthCommand({
                UserPoolId: USER_POOL_ID,
                ClientId: CLIENT_ID,
                AuthFlow: 'ADMIN_NO_SRP_AUTH',
                AuthParameters: {
                    USERNAME: email,
                    PASSWORD: password
                }
            });
            const response = await cognitoClient.send(authCommand);
            const authResult = response.AuthenticationResult;
            if (!authResult) {
                return createCorsResponse(401, {
                    error: 'Authentication failed'
                });
            }
            const accessToken = authResult.AccessToken;
            const refreshToken = authResult.RefreshToken;
            const idToken = authResult.IdToken;
            const getUserCommand = new client_cognito_identity_provider_1.GetUserCommand({
                AccessToken: accessToken
            });
            const userResponse = await cognitoClient.send(getUserCommand);
            const cognitoUserId = userResponse.Username;
            const user = await getUserByCognitoId(cognitoUserId);
            if (!user) {
                return createCorsResponse(404, {
                    error: 'User profile not found'
                });
            }
            try {
                await docClient.send(new lib_dynamodb_1.UpdateCommand({
                    TableName: USERS_TABLE,
                    Key: { id: user.id },
                    UpdateExpression: 'SET last_login = :lastLogin',
                    ExpressionAttributeValues: {
                        ':lastLogin': new Date().toISOString()
                    }
                }));
            }
            catch (error) {
                console.warn('Failed to update last login:', error);
            }
            return createCorsResponse(200, {
                message: 'Authentication successful',
                tokens: {
                    access_token: accessToken,
                    refresh_token: refreshToken,
                    id_token: idToken
                },
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    display_name: user.display_name,
                    avatar_url: user.avatar_url,
                    is_verified: user.is_verified
                }
            });
        }
        catch (error) {
            if (error.name === 'NotAuthorizedException' || error.name === 'UserNotFoundException') {
                return createCorsResponse(401, {
                    error: 'Invalid email or password'
                });
            }
            console.error('Cognito signin error:', error);
            return createCorsResponse(500, {
                error: 'Authentication failed'
            });
        }
    }
    catch (error) {
        console.error('Signin handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function refreshTokenHandler(event) {
    try {
        const body = JSON.parse(event.body || '{}');
        const refreshToken = body.refresh_token;
        if (!refreshToken) {
            return createCorsResponse(400, {
                error: 'Refresh token is required'
            });
        }
        try {
            const authCommand = new client_cognito_identity_provider_1.AdminInitiateAuthCommand({
                UserPoolId: USER_POOL_ID,
                ClientId: CLIENT_ID,
                AuthFlow: 'REFRESH_TOKEN_AUTH',
                AuthParameters: {
                    REFRESH_TOKEN: refreshToken
                }
            });
            const response = await cognitoClient.send(authCommand);
            const authResult = response.AuthenticationResult;
            if (!authResult) {
                return createCorsResponse(401, {
                    error: 'Invalid refresh token'
                });
            }
            return createCorsResponse(200, {
                message: 'Token refreshed successfully',
                tokens: {
                    access_token: authResult.AccessToken,
                    id_token: authResult.IdToken
                }
            });
        }
        catch (error) {
            console.error('Token refresh error:', error);
            return createCorsResponse(401, {
                error: 'Invalid refresh token'
            });
        }
    }
    catch (error) {
        console.error('Refresh token handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function signoutHandler(event) {
    try {
        const authHeader = event.headers?.Authorization || event.headers?.authorization || '';
        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }
        const accessToken = authHeader.substring(7);
        try {
            const signOutCommand = new client_cognito_identity_provider_1.GlobalSignOutCommand({
                AccessToken: accessToken
            });
            await cognitoClient.send(signOutCommand);
            return createCorsResponse(200, {
                message: 'Signed out successfully'
            });
        }
        catch (error) {
            console.error('Signout error:', error);
            return createCorsResponse(500, {
                error: 'Failed to sign out'
            });
        }
    }
    catch (error) {
        console.error('Signout handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
const handler = async (event, context) => {
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }
    const path = event.path;
    const method = event.httpMethod;
    try {
        if (path === '/auth/signup' && method === 'POST') {
            return await signupHandler(event);
        }
        else if (path === '/auth/signin' && method === 'POST') {
            return await signinHandler(event);
        }
        else if (path === '/auth/refresh' && method === 'POST') {
            return await refreshTokenHandler(event);
        }
        else if (path === '/auth/signout' && method === 'POST') {
            return await signoutHandler(event);
        }
        else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    }
    catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
exports.handler = handler;
