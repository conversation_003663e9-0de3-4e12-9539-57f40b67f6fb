# GameFlex AWS Backend Startup Script (PowerShell)
# This script starts the AWS backend using LocalStack Pro on Windows

param(
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load environment variables from .env file
function Load-EnvFile {
    if (Test-Path ".env") {
        Write-Status "Loading environment variables from .env file..."
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                # Remove quotes if present
                $value = $value -replace '^"(.*)"$', '$1'
                $value = $value -replace "^'(.*)'$", '$1'
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                if ($Verbose) {
                    Write-Host "  Set $name" -ForegroundColor Gray
                }
            }
        }
    }
    else {
        Write-Warning ".env file not found. Using default values."
    }
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[GAMEFLEX] $Message" -ForegroundColor Blue
}

# Check if Docker Desktop is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Status "Docker Desktop is running"
        return $true
    }
    catch {
        Write-Error "Docker Desktop is not running. Please start Docker Desktop and try again."
        return $false
    }
}

# Check if required ports are available
function Test-Ports {
    $ports = @(4566, 45660)  # Only LocalStack ports needed
    $portsInUse = @()

    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            $portsInUse += $port
            Write-Warning "Port $port is already in use"
        }
    }

    if ($portsInUse.Count -gt 0 -and -not $Force) {
        $response = Read-Host "Do you want to continue anyway? (y/N)"
        if ($response -notmatch "^[Yy]$") {
            Write-Error "Startup cancelled"
            return $false
        }
    }

    Write-Status "Port check completed"
    return $true
}

# Create necessary directories
function New-Directories {
    Write-Status "Creating necessary directories..."
    
    $directories = @(
        "init",
        "lambda-functions",
        "cloudformation", 
        "database\init",
        "logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Status "Directories created"
}

# Start Docker services
function Start-Services {
    Write-Header "Starting GameFlex AWS Backend..."
    
    try {
        # Pull latest images
        Write-Status "Pulling latest Docker images..."
        docker compose pull
        
        # Start services
        Write-Status "Starting services..."
        docker compose up -d
        
        # Wait for services to be healthy
        Write-Status "Waiting for services to be ready..."
        
        # Wait for LocalStack
        Write-Status "Waiting for LocalStack to be ready..."
        $timeout = 120
        $counter = 0
        
        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Status "LocalStack is ready"
                    break
                }
            }
            catch {
                # Continue waiting
            }
            
            Start-Sleep -Seconds 2
            $counter += 2
            
            if ($counter -ge $timeout) {
                Write-Error "LocalStack failed to start within $timeout seconds"
                docker compose logs localstack
                throw "LocalStack startup timeout"
            }
        } while ($true)
        
        # Wait for DynamoDB to be available through LocalStack
        Write-Status "Waiting for LocalStack DynamoDB to be ready..."
        Start-Sleep -Seconds 5  # Give LocalStack time to initialize DynamoDB
        
        return $true
    }
    catch {
        Write-Host "[ERROR] Failed to start services: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Initialize AWS services
function Initialize-AwsServices {
    Write-Status "Initializing AWS services..."

    if (Test-Path "init\init-aws-services.ps1") {
        Write-Status "Running AWS services initialization..."
        & ".\init\init-aws-services.ps1"
    }
    else {
        Write-Warning "AWS services initialization script not found"
        Write-Warning "You may need to run the initialization manually"
    }
}

# Clean up any existing CloudFormation stacks that might conflict
function Cleanup-ExistingResources {
    Write-Status "Cleaning up any conflicting CloudFormation stacks..."

    # Set AWS CLI environment for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    # If Force is specified, restart LocalStack completely
    if ($Force) {
        Write-Status "Force mode enabled - restarting LocalStack completely..."
        try {
            docker compose down
            Start-Sleep -Seconds 5
            docker compose up -d

            # Wait for LocalStack to be ready again
            Write-Status "Waiting for LocalStack to restart..."
            $timeout = 120
            $counter = 0

            do {
                try {
                    $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                    if ($response.StatusCode -eq 200) {
                        Write-Status "LocalStack restarted successfully"
                        break
                    }
                }
                catch {
                    # Continue waiting
                }

                Start-Sleep -Seconds 2
                $counter += 2

                if ($counter -ge $timeout) {
                    Write-Error "LocalStack failed to restart within $timeout seconds"
                    throw "LocalStack restart timeout"
                }
            } while ($true)

            # Give LocalStack a bit more time to initialize services
            Start-Sleep -Seconds 10
        }
        catch {
            Write-Error "Failed to restart LocalStack: $_"
            return $false
        }
    }

    try {
        # Check for existing stack and its status
        $stackExists = $false
        $stackStatus = ""

        $stackInfo = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].StackStatus" --output text 2>$null
        if ($LASTEXITCODE -eq 0 -and $stackInfo -and $stackInfo.Trim() -ne "") {
            $stackExists = $true
            $stackStatus = $stackInfo.Trim()
            Write-Status "Found existing stack with status: $stackStatus"
        }
        else {
            Write-Status "No existing stack found"
        }

        if ($stackExists) {
            # Handle different stack states
            switch ($stackStatus) {
                "CREATE_FAILED" {
                    Write-Status "Stack is in CREATE_FAILED state, deleting..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name gameflex-infrastructure-development
                    # Don't wait for delete completion for failed stacks, they delete quickly
                    Start-Sleep -Seconds 5
                }
                "DELETE_FAILED" {
                    Write-Status "Stack is in DELETE_FAILED state, forcing deletion..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name gameflex-infrastructure-development
                    Start-Sleep -Seconds 5
                }
                "ROLLBACK_COMPLETE" {
                    Write-Status "Stack is in ROLLBACK_COMPLETE state, deleting..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name gameflex-infrastructure-development
                    Write-Status "Waiting for stack deletion to complete..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation wait stack-delete-complete --stack-name gameflex-infrastructure-development --cli-read-timeout 120 --cli-connect-timeout 60
                }
                "CREATE_COMPLETE" {
                    Write-Status "Stack exists and is complete, deleting..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name gameflex-infrastructure-development
                    Write-Status "Waiting for stack deletion to complete..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation wait stack-delete-complete --stack-name gameflex-infrastructure-development --cli-read-timeout 120 --cli-connect-timeout 60
                }
                default {
                    Write-Status "Stack is in state: $stackStatus, attempting deletion..."
                    aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name gameflex-infrastructure-development
                    Start-Sleep -Seconds 10  # Give it some time but don't wait indefinitely
                }
            }

            # Verify stack is actually gone
            $retryCount = 0
            $maxRetries = 6
            while ($retryCount -lt $maxRetries) {
                $stackStillExists = $false
                try {
                    $stackCheck = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].StackStatus" --output text 2>$null
                    if ($LASTEXITCODE -eq 0 -and $stackCheck) {
                        $stackStillExists = $true
                        Write-Status "Stack still exists with status: $stackCheck, waiting... (attempt $($retryCount + 1)/$maxRetries)"
                        Start-Sleep -Seconds 10
                        $retryCount++
                    }
                }
                catch {
                    # Error means stack doesn't exist, which is what we want
                }

                if (-not $stackStillExists) {
                    Write-Status "Stack successfully deleted"
                    break
                }
            }

            if ($retryCount -eq $maxRetries) {
                Write-Warning "Stack may still exist after cleanup attempts"
            }
        }

        # Clean up any Lambda functions that might conflict with CloudFormation
        $lambdaFunctions = @(
            "gameflex-auth-development",
            "gameflex-posts-development",
            "gameflex-media-development",
            "gameflex-users-development"
        )

        foreach ($functionName in $lambdaFunctions) {
            try {
                $functionExists = aws --endpoint-url=$ENDPOINT_URL lambda get-function --function-name $functionName 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Status "Deleting existing Lambda function: $functionName"
                    aws --endpoint-url=$ENDPOINT_URL lambda delete-function --function-name $functionName
                    if ($LASTEXITCODE -eq 0) {
                        Write-Status "Successfully deleted Lambda function: $functionName"
                    }
                    else {
                        Write-Warning "Failed to delete Lambda function: $functionName"
                    }
                }
            }
            catch {
                # Function doesn't exist, which is fine
            }
        }

        # Clean up any IAM roles that might conflict with CloudFormation
        $iamRoles = @(
            "gameflex-lambda-execution-role-development",
            "gameflex-authenticated-role-development"
        )

        foreach ($roleName in $iamRoles) {
            try {
                $roleExists = aws --endpoint-url=$ENDPOINT_URL iam get-role --role-name $roleName 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Status "Deleting existing IAM role: $roleName"

                    # First detach any managed policies
                    $attachedPolicies = aws --endpoint-url=$ENDPOINT_URL iam list-attached-role-policies --role-name $roleName --query "AttachedPolicies[].PolicyArn" --output text 2>$null
                    if ($attachedPolicies) {
                        $attachedPolicies -split "`t" | ForEach-Object {
                            if ($_.Trim()) {
                                aws --endpoint-url=$ENDPOINT_URL iam detach-role-policy --role-name $roleName --policy-arn $_.Trim()
                            }
                        }
                    }

                    # Delete any inline policies
                    $inlinePolicies = aws --endpoint-url=$ENDPOINT_URL iam list-role-policies --role-name $roleName --query "PolicyNames" --output text 2>$null
                    if ($inlinePolicies) {
                        $inlinePolicies -split "`t" | ForEach-Object {
                            if ($_.Trim()) {
                                aws --endpoint-url=$ENDPOINT_URL iam delete-role-policy --role-name $roleName --policy-name $_.Trim()
                            }
                        }
                    }

                    # Now delete the role
                    aws --endpoint-url=$ENDPOINT_URL iam delete-role --role-name $roleName
                    if ($LASTEXITCODE -eq 0) {
                        Write-Status "Successfully deleted IAM role: $roleName"
                    }
                    else {
                        Write-Warning "Failed to delete IAM role: $roleName"
                    }
                }
            }
            catch {
                # Role doesn't exist, which is fine
            }
        }

        # Clean up any temporary files from previous manual setups
        $tempFiles = @("temp-lambda.js", "temp-lambda.zip", "lambda-trust-policy.json")
        foreach ($file in $tempFiles) {
            if (Test-Path $file) {
                Remove-Item $file -Force
                Write-Status "Removed temporary file: $file"
            }
        }

        Write-Status "Resource cleanup completed"
        return $true
    }
    catch {
        Write-Warning "Resource cleanup failed: $_"
        return $true  # Continue even if cleanup fails
    }
}





# Deploy CloudFormation infrastructure
function Deploy-Infrastructure {
    Write-Status "Deploying CloudFormation infrastructure..."

    # Set AWS CLI environment for LocalStack
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        # Check if stack still exists after cleanup
        $stackExists = $false

        $stackStatus = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].StackStatus" --output text 2>$null
        if ($LASTEXITCODE -eq 0 -and $stackStatus -and $stackStatus.Trim() -ne "") {
            $stackExists = $true
            $stackStatus = $stackStatus.Trim()
            Write-Status "Stack still exists with status: $stackStatus"

            # If stack exists but is in a failed state, try to delete it one more time
            if ($stackStatus -match "FAILED|ROLLBACK") {
                Write-Status "Attempting final cleanup of failed stack..."
                aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name gameflex-infrastructure-development
                Start-Sleep -Seconds 15

                # Check again
                $finalCheck = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].StackStatus" --output text 2>$null
                if ($LASTEXITCODE -eq 0 -and $finalCheck -and $finalCheck.Trim() -ne "") {
                    Write-Warning "Stack still exists after final cleanup attempt"
                    return $false
                }
                else {
                    Write-Status "Stack successfully deleted on final attempt"
                    $stackExists = $false
                }
            }
        }
        else {
            Write-Status "No existing stack found - ready to create new one"
        }

        if ($stackExists) {
            Write-Error "Cannot create stack - existing stack still present"
            return $false
        }

        # Create new stack
        Write-Status "Creating CloudFormation stack..."
        aws --endpoint-url=$ENDPOINT_URL cloudformation create-stack --stack-name gameflex-infrastructure-development --template-body file://cloudformation/gameflex-infrastructure.yaml --parameters file://cloudformation/parameters/development.json --capabilities CAPABILITY_IAM

        if ($LASTEXITCODE -eq 0) {
            Write-Status "Waiting for stack creation to complete..."
            aws --endpoint-url=$ENDPOINT_URL cloudformation wait stack-create-complete --stack-name gameflex-infrastructure-development --cli-read-timeout 300 --cli-connect-timeout 60

            if ($LASTEXITCODE -eq 0) {
                Write-Status "Infrastructure deployment completed successfully"
                return $true
            }
            else {
                Write-Error "Stack creation failed"
                # Show stack events for debugging
                Write-Status "Stack creation failed. Recent events:"
                aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stack-events --stack-name gameflex-infrastructure-development --query "StackEvents[?ResourceStatus=='CREATE_FAILED'].[LogicalResourceId,ResourceStatusReason]" --output table
                return $false
            }
        }
        else {
            Write-Error "Failed to initiate stack creation"
            return $false
        }
    }
    catch {
        Write-Error "Infrastructure deployment failed: $_"
        return $false
    }
}

# Display service information
function Show-ServiceInfo {
    Write-Header "GameFlex AWS Backend (LocalStack Pro) is now running!"
    Write-Host ""

    # Get API Gateway URL from CloudFormation stack outputs
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        $apiUrl = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text 2>$null
        if ($apiUrl) {
            Write-Status "API Gateway URL: $apiUrl" -ForegroundColor Cyan
        }
    }
    catch {
        # Continue if we can't get the API URL
    }

    Write-Status "Service URLs:"
    Write-Host "  LocalStack Dashboard: http://localhost:45660/_localstack/health" -ForegroundColor Cyan
    Write-Host "  LocalStack Pro Dashboard: http://localhost:4566/_localstack/health" -ForegroundColor Cyan
    Write-Host "  LocalStack HTTPS Gateway: https://localhost:443" -ForegroundColor Cyan
    Write-Host "  DynamoDB (via LocalStack): http://localhost:4566" -ForegroundColor Cyan
    Write-Host "  S3 Console: http://localhost:45660/_aws/s3" -ForegroundColor Cyan
    Write-Host "  Cognito Console: http://localhost:45660/_aws/cognito" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Development Credentials:"
    Write-Host "  Developer: <EMAIL> / DevPassword123!" -ForegroundColor Cyan
    Write-Host "  Admin: <EMAIL> / AdminPassword123!" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Useful Commands:"
    Write-Host "  Check status: docker compose ps" -ForegroundColor Cyan
    Write-Host "  View logs: docker compose logs -f" -ForegroundColor Cyan
    Write-Host "  Stop services: .\stop.ps1" -ForegroundColor Cyan
    Write-Host "  Restart: .\stop.ps1; .\start.ps1" -ForegroundColor Cyan
    Write-Host "  View CloudFormation stack: aws --endpoint-url=http://localhost:45660 cloudformation describe-stacks --stack-name gameflex-infrastructure-development" -ForegroundColor Cyan
    Write-Host ""

    Write-Warning "This is a development environment. Do not use in production!"
}

# Main execution
function Main {
    Write-Header "GameFlex AWS Backend Startup"
    Write-Host ""

    # Load environment variables from .env file
    Load-EnvFile

    if (-not (Test-Docker)) {
        exit 1
    }
    
    if (-not (Test-Ports)) {
        exit 1
    }
    
    New-Directories
    
    if (-not (Start-Services)) {
        exit 1
    }

    Initialize-AwsServices

    Cleanup-ExistingResources

    if (-not (Deploy-Infrastructure)) {
        Write-Error "Infrastructure deployment failed."
        Write-Status "You can try running with -Force to restart LocalStack completely:"
        Write-Host "  .\stop.ps1; docker system prune -f; .\start.ps1 -Force" -ForegroundColor Cyan
        exit 1
    }

    Show-ServiceInfo
    
    Write-Status "Startup completed successfully!"
}

# Run main function
try {
    Main
}
catch {
    Write-Host "[ERROR] Startup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
